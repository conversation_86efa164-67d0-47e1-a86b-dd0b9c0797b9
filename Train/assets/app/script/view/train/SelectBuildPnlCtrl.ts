import EventType from '../../common/event/EventType'
import NodeType from '../../common/event/NodeType'
import { uiHel<PERSON> } from '../../common/helper/UIHelper'
import { cfgHelper } from '../../common/helper/CfgHelper'
import { gameHelper } from '../../common/helper/GameHelper'
import { res<PERSON>elper } from '../../common/helper/ResHelper'
import { viewHelper } from '../../common/helper/ViewHelper'
import CarriageModel from '../../model/train/common/CarriageModel'
import CameraView from '../camera/CameraView'
import MainWindCtrl from '../main/MainWindCtrl'
import { WeakGuideObj } from '../../model/guide/WeakGuideModel'
import { MarkNewType, WeakGuideType } from '../../common/constant/Enums'
import { BUILD_ATTRS, DORM_RIGHT_BED_ID } from '../../common/constant/Constant'
import { anim<PERSON>elper } from '../../common/helper/AnimHelper'
import MaskRedCmpt from '../cmpt/common/MaskRedCmpt'
import BuildObj from '../../model/train/common/BuildObj'
import { BuildCfg, CarriageThemeCfg, FocusCfg } from '../../common/constant/DataType'

const colorCfg = [
    { id: 1013, color: '#9ec82e' },
    { id: 1014, color: '#6ab8da' },
    { id: 1016, color: '#6dc9c7' },
    { id: 1017, color: '#f4b346' },
    { id: 1018, color: '#f0a55c' },
    { id: 1019, color: '#82b3e9' },
    { id: 1020, color: '#6ed1e5' },
    { id: 1021, color: '#e690e9' },
    { id: 1022, color: '#f49b64' },
    { id: 1023, color: '#F3BEB4' },
]

const { ccclass } = cc._decorator
@ccclass
export default class SelectBuildPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected rootNode_: cc.Node = null // path://root_n
    protected prefabNode_: cc.Node = null // path://root_n/prefab_n
    protected itemsSv_: cc.ScrollView = null // path://root_n/items_sv
    protected unlockThemeNode_: cc.Node = null // path://root_n/items_sv/view/content/unlockTheme_n
    protected itemsNode_: cc.Node = null // path://root_n/items_sv/view/content/items_n
    protected tabsTc_: cc.ToggleContainer = null // path://root_n/tabs_tc_tce
    protected currencyLayoutNode_: cc.Node = null // path://currency_layout_n
    protected backNode_: cc.Node = null // path://back_be_n
    //@end
    public model: CarriageModel = null

    private moveY: number = 500

    private prePrefab: cc.Node = null

    private cb: Function = null

    public listenEventMaps() {
        return [
            { [EventType.UNLOCK_THEME]: this.onTheme },
            { [EventType.UNLOCK_BUILD]: () => this.closeSelect() },
            { [EventType.CHANGE_BUILD]: this.onChange },
            { [EventType.SHOW_WEAK_GUIDE]: this.onWeakGuide },

            //guide node
            { [NodeType.GUIDE_TRAIN_ITEM_INDEX_1_UNLOCK]: this.getItemFirstLvUp },
            { [NodeType.GUIDE_TRAIN_ITEM_INDEX_1_LEVELUP]: this.getItemFirstLvUp },
            { [NodeType.SELECTBUILD_CURRENCY]: this.getCurrencyNode },
        ]
    }

    public async onCreate() {
        this.setParam({ isAct: false, isMask: false })
        this.rootNode_.y = -cc.winSize.height / 2 - this.moveY
    }

    public onEnter(carriage: CarriageModel, cb?: Function) {
        this.model = carriage
        this.cb = cb
        this.showSelect()
        this.updateView()
        this.setLocation()
        this.removeNew()
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://back_be_n
    onClickBack(event: cc.Event.EventTouch, data: any) {
        this.closeSelect(true)
    }

    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    private removeNew() {
        gameHelper.new.removeNew(MarkNewType.BUILD_CAN_LVUP, [this.model.getID()])
    }

    // ----------------------------------------- custom function ----------------------------------------------------
    @ut.addLock
    private async showSelect() {
        await cc.tween(this.rootNode_).delay(0.01).to(.16, { y: -cc.winSize.height / 2 }).promise()
        eventCenter.emit(EventType.SELECT_BUILD_ENTER)
    }

    @ut.addLock
    private async closeSelect(needBack: boolean = false) {
        this.backNode_.active = false
        cc.tween(this.rootNode_).to(.2, { y: -cc.winSize.height / 2 - this.moveY }).start()
        await this.cb?.(needBack)
        this.close()
    }

    private updateView() {
        this.updateItems()
        this.setCurrency()
    }

    private async onTheme() {
        this.setCurrency()
        await ut.wait(0.1, this)
        this.updateItems(true)
    }

    private actChangeItem(it: cc.Node, name: string, isAct: boolean) {
        it.Component(cc.Layout).enabled = !isAct
        it.Swih(name)
        if (!isAct) return
        let width = it.Child(name).width
        cc.Tween.stopAllByTarget(it)
        cc.tween(it).to(0.5, { width }).start()
    }

    /**设施列表 */
    private updateItems(isAct: boolean = false) {
        let builds = cfgHelper.getBuilds(this.model.getID()).slice()
        builds = builds.filter(b => b.show) //排除不展示的
        builds.sort((a, b) => {
            return a.sortId - b.sortId
        })

        let nextTheme = this.model.getNextTheme()

        this.updateUnlockThemeItem(nextTheme)

        let prefab = this.prefabNode_
        if (this.model.getThemeLv() > 1) {
            prefab.Swih('buyPrefab')
        } else {
            prefab.Swih('lvUpPrefab')
        }

        this.itemsNode_.Items(builds, prefab, (node, config) => {
            let order = config.order
            let build = this.model.getBuildByOrder(order)
            if (build) {
                config = cfgHelper.getBuildById(build.id)
            }
            node.Data = build
            let lv = build ? build.lv : 0
            let maxLv = build?.getMaxLv()
            let isMaxLv = build ? lv >= maxLv : false
            let isRealMaxLv = !nextTheme && isMaxLv
            let it
            if (build && isMaxLv) {
                this.actChangeItem(node, 'buyPrefab', isAct)
                it = node.Child('buyPrefab')
                let unlockNode = it.Child('unlock')
                unlockNode.active = true
                let icon = it.Child('icon')
                icon.opacity = 255
                resHelper.loadBuildIcon(icon, config, this.getTag())

                it.off("click")
                it.on("click", async () => {
                    this.showDetail(config)
                })
            } else {
                this.actChangeItem(node, 'lvUpPrefab', isAct)
                it = node.Child('lvUpPrefab')
                let noMax = !isMaxLv
                let isBuy = lv == 0
                let isChange = !isBuy && lv + 1 == maxLv
                let outputNode = it.Child('output')
                outputNode.active = noMax
                if (outputNode.active) {
                    this.setOutput(outputNode, build, config)
                }

                let lvLb = it.Child('lv/count', cc.Label)
                lvLb.setLocaleKey("characterDevelop_guiText_33", isBuy ? lv + 1 : lv)
                it.Child("lv/icon").active = !isBuy

                let cfg = cfgHelper.getBuildLvCfg(this.model.getID(), order, lv + 1)
                let costs = it.Child('costs')
                costs.active = noMax && !isChange
                if (costs.active) {
                    costs.Items(gameHelper.toConditions(cfg?.buyCost), (it, data) => {
                        uiHelper.setIconNum(it, data, this.getTag())
                    })
                }

                let lvUpNode = it.Child('lvUp')
                lvUpNode.active = !isRealMaxLv
                if (lvUpNode.active) {
                    if (isBuy) {
                        if (cfg?.buyCost) {
                            lvUpNode.Child('lb', cc.Label).setLocaleKey("trainItemBuild_buttonName_3")
                        }
                        else {
                            lvUpNode.Child('lb', cc.Label).setLocaleKey("free")
                        }
                    } else if (isChange) {
                        lvUpNode.Child('lb', cc.Label).setLocaleKey("trainItemBuild_buttonName_4")
                    } else {
                        lvUpNode.Child('lb', cc.Label).setLocaleKey("trainItemBuild_buttonName_2")
                    }
                    lvUpNode.Component(cc.Button).interactable = noMax
                    if (noMax) {
                        lvUpNode.off("click")
                        lvUpNode.on("click", async () => {
                            if (isBuy) {
                                this.showDetail(config)
                            } else if (isChange) {
                                this.showDetail(build.getNextSkinJson(), true)
                            } else {
                                this.buildLvUp(build)
                            }
                        })
                    }
                }

                let needUnlockTheme = it.Child('needUnlockTheme')
                needUnlockTheme.active = isMaxLv && !!nextTheme
                if (needUnlockTheme.active) {
                    needUnlockTheme.Component(cc.Label).setLocaleUpdate(() => assetsMgr.lang("trainWork_guiText_2", assetsMgr.lang(nextTheme.name)))
                }

                let progressNode = it.Child("progress_di")
                progressNode.active = !isRealMaxLv && !isBuy
                if (progressNode.active) {
                    let progress = progressNode.Child('mask/progress')
                    let preMaxLv = 0
                    if (build) {
                        preMaxLv = build.getPreMaxLv()
                    } else {
                        const carriage = gameHelper.train.getCarriageById(config.carriageId)
                        const theme = cfgHelper.getThemes(config.carriageId).find(d => d.order == carriage.getThemeLv() - 1)
                        if (!theme) preMaxLv = 1
                        else preMaxLv = theme.unlockLevel
                    }

                    progress.width = ((lv - preMaxLv) / (maxLv - preMaxLv)) * progressNode.width
                }

                let maxLvNode = it.Child('isMaxLv')
                maxLvNode.active = isRealMaxLv

                let icon = it.Child('mask/icon')
                resHelper.loadBuildIcon(icon, config, this.getTag())
                icon.opacity = !isBuy ? 255 : 102
                icon.off("click")
                icon.on("click", () => {
                    this.showDetail(config)
                })
            }
        })
    }

    private setOutput(outputNode: cc.Node, build: BuildObj, buildCfg?: BuildCfg) {
        let lvCfg = build ? cfgHelper.getBuildLvCfg(build.carriageId, build.order, build.lv + 1) : null
        if (!lvCfg && buildCfg) {
            lvCfg = cfgHelper.getBuildLvCfg(buildCfg.carriageId, buildCfg.order, 1)
        }
        let attrs = BUILD_ATTRS
        let output = 0, index = 0
        for (index = 0; index < attrs.length; index++) {
            let attr = attrs[index]
            output = lvCfg?.add[attr] || 0
            if (output > 0) break
        }
        let item = outputNode.Child('item')
        item.Child("icon", cc.MultiFrame).setFrame(index)
        item.Child("count").setLocaleKey("trainItemBuild_guiText_2", `+${output}`)
    }

    private async buildLvUp(build) {
        let lvCfg = cfgHelper.getBuildLvCfg(build.carriageId, build.order, build.lv + 1)
        if (!uiHelper.checkBuyCost(lvCfg.buyCost)) {
            return
        }
        let succ = await this.model.buildLvUp(build.order)
        if (succ) {
            this.updateView()
        }
    }

    private updateUnlockThemeItem(cfg: CarriageThemeCfg) {
        let bol = cfg && this.model.isAllBuildsMaxLv()
        let root = this.unlockThemeNode_
        root.active = bol
        if (!bol) return
        let id = this.model.getID()
        let level = this.model.getThemeLv() + 1
        root.Component(MaskRedCmpt).init(id)
        this.model.setLabelThemeName(root.Child('name', cc.Label), cfg)
        root.Child('name').SetColor(colorCfg.find(e => e.id == id)?.color)
        resHelper.loadTmpIcon(`train/icon/theme_small_${id}_${level}`, root.Child('icon', cc.Sprite), this.getTag())
        resHelper.loadTmpIcon(`train/icon/theme_bak_${id}`, root.Child('bg', cc.Sprite), this.getTag())
        let fun = () => {
            viewHelper.showPnl('train/ThemeDetail', cfg)
            gameHelper.new.removeNew(MarkNewType.BUILD_UNLOCK_SKIN, [id])
        }
        let node = root.Child('unlock')
        node.off("click")
        node.on("click", fun)
        root.off("click")
        root.on("click", fun)
    }

    private showDetail(cfg: BuildCfg, isLvUp: boolean = false) {
        if (!cc.isValid(this, true)) {
            return
        }
        viewHelper.showPnl('train/BuildDetail', cfg, { isLvUp })
    }

    private onWeakGuide(guide: WeakGuideObj) {
        if (guide.id == WeakGuideType.BUY_RIGHT_BED_2) {
            let item = this.getItemById(DORM_RIGHT_BED_ID)
            if (!item) return
            animHelper.showWeakGuideFinger(item, guide.fingerGuide)
        } else if (guide.id == WeakGuideType.GOTO_BUY_BUILD_2) {
            if (this.model.getID() != gameHelper.weakGuide.buyBuildData?.carriageId) return
            this.checkOverGotoBuyBuild2()
            let item = this.getItemById(gameHelper.weakGuide.buyBuildData.id)
            if (!item) return
            animHelper.showWeakGuideFinger(item, guide.fingerGuide)
        } else if (guide.id == WeakGuideType.GOTO_LEVELUP_BUILD_2) {
            if (this.model.getID() != gameHelper.weakGuide.levelUpBuildData?.carriageId) return
            this.checkOverGotoLevelUpBuild2()
            let order = gameHelper.weakGuide.levelUpBuildData.order
            if (!order) return
            let item = this.getLvItemByOrder(order)
            if (!item) return
            animHelper.showWeakGuideFinger(item, guide.fingerGuide, item.Child('lvUp').getPosition())
        }
    }
    private async checkOverGotoBuyBuild2() {
        await viewHelper.waitAnyClickEnd()
        gameHelper.weakGuide.buyBuildStep = 0
    }
    private async checkOverGotoLevelUpBuild2() {
        await viewHelper.waitAnyClickEnd()
        gameHelper.weakGuide.levelUpBuildStep = 0
    }

    private async onChange(build: BuildObj, first: boolean) {
        this.updateView()
        if (!first) return
        this.closeSelect()
    }

    private setCurrency() {
        if (!this.currencyLayoutNode_) return
        this.currencyLayoutNode_.active = this.model.getThemeLv() > 1 && !this.model.isAllBuildsMaxLv()
    }

    // 默认定位到排序最靠前的未解锁设施
    private setLocation() {
        let i = this.getFirstUnlock()
        if (!i) return
        this.scrollToItem(i)
    }
    private async scrollToItem(i: number) {
        let scrollView = this.itemsSv_
        let content = scrollView.content
        await this.itemsNode_.Component(cc.Layout).updateLayout()
        await content.Component(cc.Layout).updateLayout()
        await ut.waitNextFrame(2, this)
        //uiHelper.scrollToItemHorizontal(this.itemsSv_, i)
        let max = content.width - scrollView.node.width
        if (max <= 0) return
        //这里不能用uihelper的接口是因为content里套了两层
        let it = this.itemsNode_.children[i]
        if (!it) return
        let per = (it.x - it.width * 0.85) / max
        //console.log(`it.id = ${it.Data.id}, it.x = ${it.x}, it.width = ${it.width}, max = ${max}   per = ${per}--------------`)
        scrollView.stopAutoScroll()
        scrollView.scrollTo(cc.v2(per, 0))
    }
    private getFirstUnlock() {
        let ary = this.itemsNode_.children
        for (let i = 0; i < ary.length; i++) {
            let cfg = ary[i].Data
            if (!cfg) return i
            let build = this.model.getBuildByOrder(cfg.order)
            if (!build) {
                return i
            }
        }
        //返回第一个可以升级的位置
        for (let i = 0; i < ary.length; i++) {
            let cfg = ary[i].Data
            let build = this.model.getBuildByOrder(cfg.order)
            if (build.lv < build.getMaxLv()) {
                return i
            }
        }
    }

    private getItemFirstLvUp() {
        let i = this.getFirstUnlock()
        if (i == null) return
        let item = this.itemsNode_.children[i]
        return item && item.Child('lvUpPrefab/lvUp')
    }
    private getItemById(id) {
        let ary = this.itemsNode_.children
        for (const item of ary) {
            if (item.Data && item.Data.id == id) {
                return item.Child('buyPrefab')
            }
        }
    }
    private getLvItemByOrder(order: number) {
        let ary = this.itemsNode_.children
        for (const item of ary) {
            let data = item.Data as BuildCfg
            if (data.order == order) {
                return item.Child('lvUpPrefab')
            }
        }
    }
    private getCurrencyNode() {
        return this.currencyLayoutNode_
    }
}
