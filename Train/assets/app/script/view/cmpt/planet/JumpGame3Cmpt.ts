import { HeroAction, HeroAnimation, PassengerLifeAnimation, PlanetEvent } from "../../../common/constant/Enums";
import EventType from "../../../common/event/EventType";
import { gameHelper } from "../../../common/helper/GameHelper";
import ActionTree from "../../../model/passenger/ActionTree";
import PlanetEmptyNode from "../../../model/planet/PlanetEmptyNode";
import PlanetWindCtrl from "../../planet/PlanetWindCtrl";
import HeroCmpt from "../hero/HeroCmpt";

import PlanetNodeCmpt from "./PlanetNodeCmpt";

const { ccclass } = cc._decorator;

import PlanetJumpTimeStone from "../../../model/planet/sp/PlanetJumpTimeStone";
import PlanetNodeRewardCmpt from "./PlanetNodeRewardCmpt";
import PlanetNodeRewardGroupCmpt from "./PlanetNodeRewardGroupCmpt";
import BaseLandCollider from "./jump/BaseLandCollider";
import WaterLandCollider from "./jump/WaterLandCollider";
import HeroShadowLandCollider from "./jump/HeroShadowLandCollider";
import LandColliderFactory from "./jump/LandColliderFactory";
import { CollisionResult, HERO_FOOT_WIDTH, HeroTrajectory, LandType } from "./jump/Define";

const MAX_TIME = 2
const MAX_DIS = 1000
const SHADOW_MAX = 6

type checkRes = { index?: number, pos?: cc.Vec2, break?: boolean, collider?: BaseLandCollider }

@ccclass
export default class JumpGame3Cmpt extends PlanetNodeCmpt {
    model: PlanetJumpTimeStone = null
    planetCtrl: PlanetWindCtrl = null
    heroNode: cc.Node = null

    powerSp: cc.Sprite = null
    points: cc.Node[] = []
    isJump: boolean = false
    isControl: boolean = false
    touchTime: number = 0

    speed: cc.Vec2 = cc.v2()
    gravity: number = 2000
    minY: number = -500

    prePos: cc.Vec2 = cc.v2()
    nextPos: cc.Vec2 = cc.v2()
    intersectPos: cc.Vec2 = cc.v2()
    resetPos: cc.Vec2 = null
    // 新的碰撞检测系统
    private landColliders: BaseLandCollider[] = []

    actionTree: ActionTree = null

    // 复制的下一关的节点
    copyNextNode: cc.Node = null
    // 有没有继承上一关hero的位置过来
    isExtendPrev: boolean = false

    // 影子节点组
    shadowNodes: cc.Node[] = []

    // 用在瀑布地块 hero被水拖着走之后  就不再与瀑布判断了
    ignoreCollider: BaseLandCollider = null


    get curIndex() { return this.model.progress }
    set curIndex(val: number) { this.model.progress = val }


    public listenEventMaps() {
        return [
            { [EventType.PLAENT_CONTROL_TOUCH_START]: this.onTouchStart },
            { [EventType.PLAENT_CONTROL_TOUCH_END]: this.onTouchEnd },
            { [EventType.PLAENT_CONTROL_TOUCH_CANCEL]: this.onTouchEnd },
            { [EventType.PLAENT_CHANGE_JUMP_END]: this.onChange },
            { [EventType.TARGET_PLANET_NODE]: this.onTarget },
        ]
    }

    public async init(model: PlanetJumpTimeStone, planetCtrl: PlanetWindCtrl) {
        super.init(model)
        this.planetCtrl = planetCtrl
        this.heroNode = planetCtrl.getHeroNode()
        // 用来做碰撞检测
        this.heroNode.width = 100
        this.heroNode.height = 130

        this.listenEventMaps()
        this.powerSp = this.heroNode.Child('ui/power/bar', cc.Sprite)
        this.powerSp.node.parent.parent.y = -200

        const end = this.node.Child(LandType.End)
        if (end) {
            end.parent = null
        }
        this.points = this.node.children

        this.landColliders = this.points.map((point, index) => LandColliderFactory.createCollider(point, -1))
        let idx = 0
        this.getVaildLands().forEach(cmpt => cmpt.index = idx++)

        this.actionTree = new ActionTree().init(this)

        const mapNode = this.planetCtrl.getMapNode()
        let curIndex = mapNode.children.indexOf(this.node)
        if (curIndex > 0) {
            const prev = mapNode.children[curIndex - 1]
            if (prev && prev.Component(JumpGame3Cmpt) != null) {
                this.isExtendPrev = true
            }
        }
        this.model.reachOffset = ut.convertToNodeAR(this.points[0], this.node)
        this.model.endOffset = ut.convertToNodeAR(this.points.last(), this.node)

        if (model.shadows?.length) {
            model.shadows.slice(0, SHADOW_MAX).forEach(shadow => this.createShadowNode(shadow.x, shadow.y, shadow.index, false))
        }

        if (gameHelper.hero.getTargetModel() == this.model) {
            this.onTarget(this.model)
        }

        this.node.zIndex = 0
        this.initRewards()
        eventCenter.emit(EventType.PLANET_JUMP_SHOW_TV)
    }


    async onTarget(model: PlanetEmptyNode) {
        if (this.model != model) return
        // 将下一关卡的第一个节点补充到当前
        const mapNode = this.planetCtrl.getMapNode()
        let curIndex = mapNode.children.indexOf(this.node)
        if (curIndex < mapNode.childrenCount - 1) {
            const next = mapNode.children[curIndex + 1]
            if (next && next.Component(JumpGame3Cmpt) != null && next.childrenCount > 0) {
                const first = next.children[0]
                const to = cc.instantiate(first)
                to.parent = this.node
                to.setPosition(ut.convertToNodeAR(first, this.node))
                let index = this.getVaildLandNodes().length
                this.landColliders.push(LandColliderFactory.createCollider(to, index))
                // 更新endOffset
                this.model.endOffset = ut.convertToNodeAR(this.points.last(), this.node)
                this.copyNextNode = to
            }
        }

        if (!this.isExtendPrev) {
            const pos = this.getFudaoCenter(this.model.shadowIndex || this.curIndex)
            gameHelper.hero.setPosition(pos)
        }

        this.planetCtrl.focusHero({ back: true })
        gameHelper.hero.setAction(HeroAction.IDLE)
        this.setControl(true)
        gameHelper.hero.setAction(HeroAction.JUMP_GAME3)
    }

    getFudaoCenter(index: number) {
        let point: cc.Node = null
        if (this.model.isVirtualIndex(index)) {
            // 恢复到影子的位置
            point = this.shadowNodes.find(it => it.Data?.index == index)
        }
        if (!point) {
            point = this.getVaildLandNodes().find(p => p.Component(BaseLandCollider).index == index)
        }
        const line = point.Child("line")
        const startPos = this.node.getPosition()
        const center = ut.convertToNodeAR(line, this.node)
        return cc.v2(startPos.x + center.x, startPos.y + center.y)
    }

    setControl(bol: boolean) {
        this.isControl = bol
        eventCenter.emit(EventType.PLAENT_CAN_CONTROL_JUMP, bol)
    }

    setSpeedByDis(dis: number) {
        let tan = ut.tan(60)
        this.speed.x = Math.sqrt(this.gravity * dis / (2 * tan))
        this.speed.y = this.speed.x * tan
    }

    onChange(x: number) {
        this.setSpeedByDis(x - this.heroNode.x)
    }

    showPower(bol: boolean) {
        this.powerSp.fillRange = 0
        this.powerSp.node.parent.active = bol
    }

    updatePower() {
        if (this.touchTime === 0) return
        let passTime = (Date.now() - this.touchTime) * 0.001
        let per = passTime / MAX_TIME
        if (per > 1) per = 1
        this.powerSp.fillRange = per
    }


    initRewards() {
        const rewardNode = this.planetCtrl.getTimeStoneRewardPrefab()
        if (!rewardNode) return
        this.points.forEach((point, i) => {
            if (!this.model.checkRewardByPoint(i)) return
            if (point.Child(rewardNode.name)) return
            let node = cc.instantiate2(rewardNode, point)
            node.active = true
            node.setPosition(cc.v2(0, point.height))
        })
    }

    async checkClaimReward() {
        const model = this.model
        if (!model.checkRewardByPoint(this.curIndex)) return
        const rewardIndex = model.getRewardIndex(this.curIndex)
        const succ = await model.claimNodeReward(rewardIndex)
        if (succ) {
            this.flyReward(this.model.rewards[rewardIndex])
        }
        return succ
    }

    async flyReward(reward) {
        const point = this.points[this.curIndex]
        const name = this.planetCtrl.getTimeStoneRewardPrefab().name
        const node = point.Child(name)
        ut.convertParent(node, this.planetCtrl["mapNode_"])
        node.addComponent(PlanetNodeRewardCmpt).init(reward)

        let items = [node]
        let go = (items: cc.Node[]) => {
            if (items.length <= 0) return
            let node = items[0]
            let cmpt = node.addComponent(PlanetNodeRewardGroupCmpt)
            return new Promise((r) => {
                cmpt.init(items, r)
                cmpt.fly()
            })
        }
        await Promise.all([go(items)])
    }

    private clampDis(dis) {
        let eventName = this.model.eventName
        if (eventName == PlanetEvent.JUMP_1 || eventName == PlanetEvent.JUMP_2) return dis

        let lands = this.getVaildLands()
        if (this.curIndex != lands.length - 1) return dis

        const land = lands.last()
        let maxX = land.getRightMax()
        let maxDis = maxX - this.prePos.x
        return Math.min(dis, maxDis)
    }

    getBirthPointIndex() {
        let index = this.points.slice(0, this.curIndex + 1).findIndex(p => p.name.includes("rebirth"))
        if (index < 0) {
            return 0
        }
        return index
    }

    check(prePos: cc.Vec2, nextPos: cc.Vec2) {
        const trajectory: HeroTrajectory = {
            leftFoot: {
                start: cc.v2(prePos.x - HERO_FOOT_WIDTH, prePos.y),
                end: cc.v2(nextPos.x - HERO_FOOT_WIDTH, nextPos.y)
            },
            rightFoot: {
                start: cc.v2(prePos.x + HERO_FOOT_WIDTH, prePos.y),
                end: cc.v2(nextPos.x + HERO_FOOT_WIDTH, nextPos.y)
            },
            // hero的碰撞盒要做一个偏移
            offset: cc.v2(nextPos.x - prePos.x, nextPos.y - prePos.y),
            worldPoints: null
        }
        return this.checkIntersect(trajectory)
    }

    onInteractInfo(interactInfo: checkRes) {
        let nextPos = this.nextPos
        if (!interactInfo) {
            return void this.heroNode.setPosition(nextPos)
        }
        if (interactInfo.break) {
            switch (interactInfo.collider.type) {
                case LandType.Die:
                    this.handleDieBreak(interactInfo)
                    break
                case LandType.Water:
                    this.handleWaterBreak(interactInfo)
                    break
            }
            return
        }
        this.handleInteract(interactInfo)
    }

    updatePos(dt: number) {
        let interactInfo: checkRes = null
        let tot = dt
        let stepDt = 1 / 120
        while (tot) {
            stepDt = Math.min(stepDt, tot)
            tot -= stepDt
            this.prePos = this.heroNode.getPosition(this.prePos)

            this.speed.y -= this.gravity * stepDt
            this.nextPos.x = this.prePos.x + this.speed.x * stepDt
            this.nextPos.y = this.prePos.y + this.speed.y * stepDt

            interactInfo = this.check(this.prePos, this.nextPos)
            if (interactInfo) {
                break
            }
            this.heroNode.setPosition(this.nextPos)
        }

        this.onInteractInfo(interactInfo)
    }

    checkIntersect(trajectory: HeroTrajectory): checkRes | null {
        let closestCollision: checkRes = null
        let closestDistance = Infinity
        for (let i = 0; i < this.landColliders.length; i++) {
            const cmpt = this.landColliders[i]
            if (!cmpt.node.active) continue
            if (cmpt == this.ignoreCollider) continue
            // if (i < this.curIndex) continue
            // hero世界多边形坐标 在需要的时候再计算
            if (trajectory.worldPoints == null && cmpt.isNeedHeroCollider()) {
                const offset = trajectory.offset
                const heroCollider = this.heroNode.Component(cc.PolygonCollider)
                trajectory.worldPoints = heroCollider.points.map(point => {
                    const localPoint = cc.v2(point.x + heroCollider.offset.x + offset.x, point.y + heroCollider.offset.y + offset.y)
                    return this.heroNode.convertToWorldSpaceAR(localPoint)
                })
            }
            const result = cmpt.intersectWith(trajectory)
            if (result && result.distance < closestDistance) {
                closestCollision = { pos: result.point, collider: cmpt }

                if (cmpt.type == LandType.Die || cmpt.type == LandType.Water) {
                    closestCollision.break = true
                }
                else {
                    closestDistance = result.distance
                    let index = cmpt.index
                    if (cmpt.type == LandType.Shadow) {
                        index = cmpt.node.Data.index
                    }
                    closestCollision.index = index
                }
            }
        }
        return closestCollision
    }

    // 红色死亡地块
    handleDieBreak(interactInfo: checkRes) {
        this.isJump = false
        let to = interactInfo.pos || this.heroNode.getPosition()
        to = ut.convertToNodeAR(this.planetCtrl.getMapNode(), this.node, to)
        this.dieBreakReset(to)
    }

    // 瀑布水流地块
    handleWaterBreak(interactInfo: checkRes) {
        this.isJump = false
        const cmpt = interactInfo.collider as WaterLandCollider
        this.heroNode.setPosition(this.nextPos)
        this.prePos = this.heroNode.getPosition(this.prePos)

        const toPos = ut.convertToNodeAR(this.planetCtrl.getMapNode(), cmpt.attachNode, this.prePos)
        const copy = cc.instantiate2(this.heroNode, cmpt.attachNode)
        copy.setPosition(toPos)
        cmpt.onAttachPosChanged = (x: number, y: number) => {
            this.prePos = this.heroNode.getPosition(this.prePos)
            this.nextPos.x = this.prePos.x + x
            this.nextPos.y = this.prePos.y + y
            this.ignoreCollider = cmpt
            const info = this.check(this.prePos, this.nextPos)
            this.onInteractInfo(info)
            this.ignoreCollider = null
            if (info) {
                this.heroNode.opacity = 255
                return false
            }
            if (this.checkDead()) {
                this.heroNode.opacity = 255
                this.reset()
                return false
            }
            return true
        }
        cmpt.beforeAttachRemove = () => {
            const rePos = ut.convertToNodeAR(cmpt.attachNode, this.planetCtrl.getMapNode(), copy.getPosition())
            this.heroNode.setPosition(rePos)
            this.heroNode.opacity = 255
            if (this.checkDead()) {
                return void this.reset()
            }
            this.ignoreCollider = cmpt
            this.prePos = this.heroNode.getPosition(this.prePos)
            this.nextPos = this.heroNode.getPosition(this.nextPos)
            const info = this.check(this.prePos, this.nextPos)
            this.onInteractInfo(info)
            this.ignoreCollider = null
            if (!info) {
                this.reset()
            }
            else {
            }
        }


        this.heroNode.opacity = 0
        cmpt.attachToCenter = true
    }

    // 通用碰撞
    handleInteract(interactInfo: checkRes) {
        const isVirtualIndex = this.model.isVirtualIndex(interactInfo.index)
        if (!isVirtualIndex) {
            this.curIndex = interactInfo.index
            this.model.shadowIndex = 0
        }
        else {
            this.model.shadowIndex = interactInfo.index
        }
        this.isJump = false
        this.heroNode.setPosition(interactInfo.pos)
        let cmpt = this.heroNode.Component(HeroCmpt)
        let p1 = cmpt.playAnimation(HeroAnimation.JUMP4).then(() => {
            this.heroNode.Component(HeroCmpt).playAnimation(PassengerLifeAnimation.IDLE, true)
        })
        let p2 = this.checkClaimReward()
        Promise.all([p1, p2]).then(() => {
            if (!isVirtualIndex) {
                this.model.setProgress(this.curIndex)
            }
            this.setControl(true)
        })
    }

    checkDead() {
        if (this.heroNode.y <= this.minY) {
            return true
        }
    }

    isMain() { return this.model.eventName == PlanetEvent.JUMP_1 || this.model.eventName == PlanetEvent.JUMP_2 }

    reset() {
        this.setControl(true)
        this.isJump = false
        if (this.isMain()) {
            this.heroNode.setPosition(this.resetPos)
        }
        else {
            let pointIndex = this.getBirthPointIndex()
            this.heroNode.setPosition(this.getFudaoCenter(pointIndex))
            this.curIndex = pointIndex
            this.model.shadowIndex = 0
        }
        this.heroNode.Component(HeroCmpt).playAnimation(PassengerLifeAnimation.IDLE, true)
    }

    async dieBreakReset(pos: cc.Vec2) {
        cc.Tween.stopAllByTarget(this.heroNode)
        this.heroNode.opacity = 255
        await cc.tween(this.heroNode).to(.2, { opacity: 0 }).start().promise()

        this.updateShadowNode(pos.x, pos.y)
        await ut.wait(.5, this)
        this.heroNode.opacity = 255
        this.reset()
    }

    createShadowNode(x: number, y: number, index: number, save: boolean = true): cc.Node {
        const prefab = this.planetCtrl.getHeroJumpDiePrefab()
        const it = cc.instantiate2(prefab, this.node)
        it.setPosition(x, y)
        this.landColliders.push(LandColliderFactory.createCollider(it, -1))
        it.Component(sp.Skeleton).playAnimation("loop", true)
        const data = { x, y, index }
        it.Data = data
        this.shadowNodes.push(it)
        if (save) {
            this.model.shadows.push(data)
        }
        return it
    }

    updateShadowNode(x: number, y: number): cc.Node {
        const shadows = this.model.shadows
        if (shadows.length < SHADOW_MAX) {
            return this.createShadowNode(x, y, this.model.nextShadowIndex())
        }
        const it = this.shadowNodes.shift()
        this.model.shiftShadow(it.Data)
        it.setPosition(x, y)
        it.Component(HeroShadowLandCollider).reset()
        const data = { x, y, index: this.model.nextShadowIndex() }
        it.Data = data
        this.shadowNodes.push(it)
        this.model.shadows.push(data)
        return it
    }

    // ============ after are events

    private onTouchStart(_event: cc.Event.EventTouch) {
        if (!this.isControl) return
        this.touchTime = Date.now()
        this.heroNode.Component(HeroCmpt).playAnimation(HeroAnimation.JUMP2, true)
        this.showPower(true)
        gameHelper.hero.setJump()
    }

    private onTouchEnd(_event: cc.Event.EventTouch) {
        if (!this.isControl || this.touchTime <= 0) {
            return
        }

        this.prePos = this.heroNode.getPosition(this.prePos)
        this.resetPos = this.heroNode.getPosition(this.resetPos)
        // 按住多少秒
        let time = (Date.now() - this.touchTime) * 0.001
        time = cc.misc.clampf(time, 0, MAX_TIME)

        let dis = time / MAX_TIME * MAX_DIS
        dis = this.clampDis(dis)
        this.setSpeedByDis(dis)

        this.isJump = true
        this.setControl(false)
        this.touchTime = 0
        this.showPower(false)
        this.heroNode.Component(HeroCmpt).playAnimation(HeroAnimation.JUMP3)
    }

    // ======== update

    update(dt: number) {
        super.update(dt)
        if (!this.model) return
        this.updatePower()
        this.actionTree && this.actionTree.update(dt)
        if (!this.isJump) return
        this.updatePos(dt)
        if (this.checkDead()) {
            this.reset()
            return
        }

        const rightLand = this.getVaildLands()
        if (this.curIndex >= rightLand.length - 1) {
            let model = this.model
            this.enabled = false
            gameHelper.hero.setPosition(this.heroNode.getPosition())
            // 重置
            if (this.copyNextNode) {
                this.copyNextNode.Component(BaseLandCollider).index = 0
            }
            ut.wait(0.5, this).then(async () => {
                this.planetCtrl.focusHero()
                await model.die()
                model.end()
            })
        }
    }

    public getVaildLands() {
        return this.landColliders.filter(l => l.type == LandType.Normal || l.type == LandType.Rebirth)
    }
    public getVaildLandNodes() {
        return this.getVaildLands().map(l => l.node)
    }

}
