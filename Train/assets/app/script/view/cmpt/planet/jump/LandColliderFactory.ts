import BaseLandCollider from "./BaseLandCollider"
import DieLand<PERSON><PERSON>ider from "./DieLandCollider"
import HeroS<PERSON>owLandCollider from "./HeroShadowLandCollider"
import NormalLandCollider from "./NormalLandCollider"
import Rebirth<PERSON>andCollider from "./RebirthLandCollider"
import WaterLandCollider from "./WaterLandCollider"
import { CollisionResult, HERO_FOOT_WIDTH, HeroTrajectory, LandType } from "./Define";

export default class LandColliderFactory {
    private static colliderMap = new Map<LandType, new () => BaseLandCollider>([
        [LandType.Normal, NormalLandCollider],
        [LandType.Shadow, HeroShadowLandCollider],
        [LandType.Die, DieLandCollider],
        [LandType.Water, WaterLandCollider],
        [LandType.Rebirth, RebirthLandCollider],
    ])

    static createCollider(point: cc.Node, index: number): BaseLandCollider {
        let landType = this.determineLandType(point)
        const ColliderClass = this.colliderMap.get(landType) || NormalLandCollider
        let cmpt = point.getComponent(ColliderClass) || point.addComponent(ColliderClass)
        cmpt.index = index
        return cmpt
    }

    private static determineLandType(point: cc.Node): LandType {
        const nodeName = point.name.toLowerCase()
        if (nodeName.includes(LandType.Shadow)) return LandType.Shadow
        if (nodeName.includes(LandType.Die)) return LandType.Die
        if (nodeName.includes(LandType.Water)) return LandType.Water
        if (nodeName.includes(LandType.Rebirth)) return LandType.Rebirth

        return LandType.Normal
    }
}